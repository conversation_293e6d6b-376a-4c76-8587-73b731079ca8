/* Font definitions updated to use fallback fonts instead of missing Nimbus fonts */

body {
    font-size: 14px;
    color: #5a5a5a;
    background-color: #E9E9E9;
    font-family: 'Arial', 'Helvetica', sans-serif;
}

p {
    line-height: 1.5;
    margin-bottom: 10px;
}

    p a {
        text-decoration: underline;
    }

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: 10px;
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-weight: bold;
}

h1 {
    font-size: 30px;
}

a {
    color: #3594DF;
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;
}

    a:focus, a:hover {
        color: #336799;
    }

::-moz-selection {
    color: #fff;
    background: #336799;
    text-shadow: none;
}

::selection {
    color: #fff;
    background: #336799;
    text-shadow: none;
}

img::selection {
    color: #fff;
    background: transparent;
}

img::-moz-selection {
    color: #fff;
    background: transparent;
}

.error-notification {
    min-height: 320px
}

    .error-notification h2 {
        font-size: 5em;
        padding: 30px 0 10px 0
    }

    .error-notification h5 {
        background-color: #cb3837;
        color: #fff;
        display: block;
        font-size: 1.4em;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 30px;
        padding: 10px 20px;
        color: #5a5a5a;
        font-size: 30px;
        background: 0 0;
        padding: 0;
        color: #cb3837
    }


.hide {
    display: none !important
}

.show {
    display: block !important
}

.container-fluid {
    padding-right: 60px;
    padding-left: 60px;
}

    .container-fluid.md-width {
        max-width: 620px;
        padding: 0;
    }

    .container-fluid.sm-width {
        max-width: 460px;
        padding: 0;
    }

.btn {
    font-size: 16px;
    padding: 15px 0;
    line-height: 16px;
    letter-spacing: -0.4px;
    width: 100%;
    text-align: center;
    font-weight: normal;
    margin-bottom: 0 !important;
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-weight: bold;
    border-radius: 30px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
}

.btn-primary {
    background-color: #3594DF;
    border-color: #3594DF;
}

    .btn-primary:hover, .btn-primary:focus, .btn-primary:active {
        color: #fff;
        background-color: #336799 !important;
        border-color: #336799 !important;
    }

.btn-lg {
    font-size: 16px;
    padding: 25px 35px;
}

.icon-user {
    background: url("images/user.png") no-repeat;
}

.icon-lock {
    background: url("images/lock.png") no-repeat;
}

.icon-eye {
    background: url("images/eye.png") no-repeat;
}

.icon-eye-slash {
    background: url("images/eye-slash.png") no-repeat;
}

header.masthead {
    background: url("images/header-bg.png") no-repeat;
    background-color: #336799;
    background-attachment: scroll;
    padding-top: 37px;
    height: 200px;
}

    header.masthead .user-header-info .user-header-wrapper {
        text-align: right;
        color: #fff;
        padding-right: 20px;
    }

        header.masthead .user-header-info .user-header-wrapper a.btn {
            width: 92px;
            background: #fff;
            font-size: 13px;
            padding: 5px;
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-weight: bold;
            margin-top: 10px;
        }

    header.masthead .user-header-info .user-header-thumb {
        width: 60px;
    }

        header.masthead .user-header-info .user-header-thumb .user-thumb-wrapper {
            height: 60px;
            background: #3594DF;
            border-radius: 5px;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
        }

            header.masthead .user-header-info .user-header-thumb .user-thumb-wrapper img {
                width: 60px;
                height: 60px;
                border-radius: 5px;
                -webkit-border-radius: 5px;
                -moz-border-radius: 5px;
            }

#questionHeader.fixed-top {
    background-color: #fff;
    padding: 20px;
    border-bottom: 2px solid #c0c1c1;
}

.main-content {
    margin-top: -70px;
    margin-bottom: 85px;
}

    .main-content .logo-bg {
        background: url("images/logo-bg.png") no-repeat 100% 0;
    }

    .main-content .content {
        background-color: #fff;
        padding: 50px 60px;
        border-radius: 50px;
        -webkit-border-radius: 10px;
        -moz-border-radius: 10px;
        box-shadow: 0 30px 60px -30px #336799;
        -webkit-box-shadow: 0 30px 60px -30px #336799;
        -moz-box-shadow: 0 30px 60px -30px #336799;
    }

    .main-content .md-width .content {
        padding: 80px;
    }

    .main-content .sm-width .content {
        padding: 40px 80px 30px 80px;
        -webkit-border-top-left-radius: 10px;
        -webkit-border-top-right-radius: 10px;
        -moz-border-radius-topleft: 10px;
        -moz-border-radius-topright: 10px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        -webkit-border-bottom-right-radius: 0;
        -webkit-border-bottom-left-radius: 0;
        -moz-border-radius-bottomright: 0;
        -moz-border-radius-bottomleft: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
    }

        .main-content .sm-width .content.login-footer {
            background-color: #f2f3f4;
            padding: 25px 80px;
            text-align: center;
            -webkit-border-top-left-radius: 0;
            -webkit-border-top-right-radius: 0;
            -moz-border-radius-topleft: 0;
            -moz-border-radius-topright: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            -webkit-border-bottom-right-radius: 10px;
            -webkit-border-bottom-left-radius: 10px;
            -moz-border-radius-bottomright: 10px;
            -moz-border-radius-bottomleft: 10px;
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }

.questions-page .question-heading {
    font-family: 'Arial', 'Helvetica', sans-serif;
}

    .questions-page .question-heading strong {
        color: #336799;
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-weight: bold;
    }

        .questions-page .question-heading strong.unsure {
            color: #ffaf2f;
        }

.questions-page .remaining-time {
    width: 234px;
}

    .questions-page .remaining-time div {
        border: 1px solid #eb4043;
        border-radius: 25px;
        -webkit-border-radius: 25px;
        -moz-border-radius: 25px;
        font-size: 16px;
        padding: 12px 27px;
    }

        .questions-page .remaining-time div span {
            display: inline-block;
        }

            .questions-page .remaining-time div span.wg-countdown {
                margin-left: 10px;
                font-family: 'Arial', 'Helvetica', sans-serif;
                font-weight: bold;
                color: #000;
            }

.questions-page .question-list {
    width: 123px;
    margin-left: 24px;
}

    .questions-page .question-list a {
        display: block;
        padding: 9px 55px 9px 24px;
        font-size: 16px;
        color: #fff;
        line-height: 16px;
        background: url("../Images/icons/cube.png") no-repeat #3594DF 77px center;
        text-align: right;
        border-radius: 25px;
        -webkit-border-radius: 25px;
        -moz-border-radius: 25px;
    }

        .questions-page .question-list a:hover {
            color: #fff;
            text-decoration: none;
        }

.questions-page .sizing-text {
    padding-bottom: 12px;
    border-bottom: 1px solid #c0c1c1;
    margin-bottom: 25px;
}

    .questions-page .sizing-text .sizing-text-list {
        list-style: none;
        padding: 0;
        margin: 0;
        margin-left: 10px;
    }

        .questions-page .sizing-text .sizing-text-list li {
            line-height: 14px;
            margin-right: 10px;
            display: inline-block;
        }

            .questions-page .sizing-text .sizing-text-list li a {
                color: #5a5a5a !important;
            }

                .questions-page .sizing-text .sizing-text-list li a.fontSmall {
                    font-size: 12px;
                }

                .questions-page .sizing-text .sizing-text-list li a.fontLarge {
                    font-size: 16px;
                }

                .questions-page .sizing-text .sizing-text-list li a:hover {
                    text-decoration: none;
                }

            .questions-page .sizing-text .sizing-text-list li.current {
                color: #c0c1c1;
            }

                .questions-page .sizing-text .sizing-text-list li.current a {
                    color: #c0c1c1 !important;
                }

body.fontSmall section.page-section {
    font-size: 12px !important;
}

body.fontLarge section.page-section {
    font-size: 16px !important;
}

.content-footer {
    margin-top: 25px;
    border-top: 1px solid #c0c1c1;
    padding-top: 25px;
}

    .content-footer .btn {
        width: 208px;
        color: #fff !important;
    }

    .content-footer .prev-question .btn {
        text-align: left;
        padding-left: 59px;
        background: url("../Images/icons/arrow-prev.png") 24px center no-repeat #eb4043;
        border: 1px solid #eb4043;
    }

        .content-footer .prev-question .btn:hover {
            background-color: #c91518 !important;
            border-color: #c91518 !important;
        }

    .content-footer .hesitate {
        text-align: center;
    }

        .content-footer .hesitate .btn {
            background: #ffaf2f;
            border: 1px solid #ffaf2f;
        }

            .content-footer .hesitate .btn:hover {
                background-color: #e28b00 !important;
                border-color: #e28b00 !important;
            }

    .content-footer .next-question {
        text-align: right;
    }

        .content-footer .next-question .btn {
            text-align: right;
            padding-right: 71px;
            background: url("../Images/icons/arrow-next.png") 160px center no-repeat #3594DF;
            border: 1px solid #3594DF;
        }

.question-popup ul {
    list-syle: none;
    padding: 0;
    margin: 0;
}

    .question-popup ul li {
        position: relative;
        list-syle: none;
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 1px solid #858585;
        margin-bottom: 20px;
        margin-right: 18px;
        border-radius: 7px;
        -webkit-border-radius: 7px;
        -moz-border-radius: 7px;
        font-size: 24px;
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-weight: bold;
        text-align: center;
    }

        .question-popup ul li a {
            color: #336799 !important;
        }

            .question-popup ul li a:hover {
                color: #3594DF !important;
                text-decoration: none;
            }

        .question-popup ul li span {
            display: block;
            width: 22px;
            height: 22px;
            font-size: 14px;
            background: #e5e5e5;
            border: 1px solid #e5e5e5;
            position: absolute;
            top: -11px;
            right: -11px;
            border-radius: 50%;
            -webkit-border-radius: 50%;
            -moz-border-radius: 50%;
        }

        .question-popup ul li.selected {
            background: #333333;
            border-color: #333333;
        }

            .question-popup ul li.selected a {
                color: #fff !important;
            }

                .question-popup ul li.selected a:hover {
                    color: #fff !important;
                }

            .question-popup ul li.selected span {
                background: #fff;
                border-color: #333333;
                color: #333333;
            }

        .question-popup ul li.unsure {
            background: #ffaf2f;
            border-color: #ffaf2f;
        }

            .question-popup ul li.unsure span {
                border-color: #ffaf2f;
                color: #ffaf2f;
            }

        .question-popup ul li.current {
            background: #336799;
            border-color: #336799;
        }

            .question-popup ul li.current span {
                border-color: #336799;
                color: #336799;
            }

.error-message {
    color: #eb4043;
    padding-bottom: 11px;
    border-bottom: 1px solid #c0c1c1;
    margin-bottom: 7px;
}

.invalid-feedback {
    font-size: 10px;
    line-height: 10px;
    color: #eb4043;
}

    .invalid-feedback .validation-summary-errors {
        font-size: 12px;
        line-height: 12px
    }

        .invalid-feedback .validation-summary-errors ul {
            padding: 0;
            list-style: none
        }

form {
    width: 100%;
}

    form .icon-bg {
        background-position: 0 11px;
    }

    form .field-title {
        font-size: 10px;
        line-height: 10px;
        position: absolute;
        top: 0;
        left: 0;
        font-family: 'Arial', 'Helvetica', sans-serif;
        font-weight: bold;
        color: #000;
    }

        form .field-title.focustext, form .field-title.focustexton {
            color: #3594DF;
        }

        form .field-title.is-invalid {
            color: #eb4043;
        }

    form .form-field {
        position: relative;
    }

    form .icon-remove {
        cursor: pointer;
        width: 16px;
        height: 16px;
        display: block;
        background: url("../Images/icons/times.png") no-repeat;
        position: absolute;
        right: 0;
        top: 15px;
    }

    form .form-control {
        font-size: 14px;
        line-height: 14px;
        border: 0;
        border-bottom: 1px solid #c0c1c1;
        padding: 15px 0 10px 0;
        color: #5a5a5a;
        border-radius: 0px;
        -webkit-border-radius: 0px;
        -moz-border-radius: 0px;
    }

        form .form-control:focus {
            border-color: #336799;
        }

        form .form-control.is-invalid {
            border-color: #eb4043 !important;
        }

            form .form-control:focus, form .form-control.is-invalid:focus {
                box-shadow: none;
                color: #151515;
            }

        form .form-control#password + .icon-remove {
            right: 41px;
        }

    form .form-control-plaintext {
        font-size: 14px;
        line-height: 14px;
        border: 0;
        border-bottom: 1px solid #c0c1c1;
        padding: 15px 0 10px 0;
        color: #5a5a5a;
    }

    form.login-form label {
        padding: 0;
        width: 42px;
    }

    form.login-form .form-control {
        width: 257px;
    }

    form.login-form .icon {
        cursor: pointer;
        width: 24px;
        height: 20px;
        display: block;
        position: absolute;
        top: 13px;
        right: 0;
    }

    form.login-form .forgot-password {
        position: absolute;
        right: 0;
        bottom: 0;
    }

    form.konfirmasi-form {
        margin-top: 20px;
    }

        form.konfirmasi-form .form-control {
            border-color: #336799;
        }

        form.konfirmasi-form .btn {
            margin-top: 20px;
        }

.konfirmasi-form .icon-bg {
    background-position: 0 11px
}

.konfirmasi-form .field-title {
    font-size: 10px;
    line-height: 10px;
    position: absolute;
    top: 0;
    left: 0;
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-weight: bold;
    color: #000
}

    .konfirmasi-form .field-title.focustext, .konfirmasi-form .field-title.focustexton {
        color: #3594df
    }

    .konfirmasi-form .field-title.is-invalid {
        color: #eb4043
    }

.konfirmasi-form .form-field {
    position: relative
}

.konfirmasi-form .icon-remove {
    cursor: pointer;
    width: 16px;
    height: 16px;
    display: block;
    background: url(../Images/icons/times.png) no-repeat;
    position: absolute;
    right: 0;
    top: 15px
}

.konfirmasi-form .form-control {
    font-size: 14px;
    line-height: 14px;
    border: 0;
    border-bottom: 1px solid #c0c1c1;
    padding: 15px 0 10px 0;
    color: #5a5a5a;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0
}

    .konfirmasi-form .form-control:focus {
        border-color: #336799
    }

    .konfirmasi-form .form-control.is-invalid {
        border-color: #eb4043 !important
    }

        .konfirmasi-form .form-control.is-invalid:focus, .konfirmasi-form .form-control:focus {
            box-shadow: none;
            color: #151515
        }

    .konfirmasi-form .form-control#password + .icon-remove {
        right: 41px
    }

.konfirmasi-form .form-control-plaintext {
    font-size: 14px;
    line-height: 14px;
    border: 0;
    border-bottom: 1px solid #c0c1c1;
    padding: 15px 0 10px 0;
    color: #5a5a5a
}

.container-fluid form .form-control {
    border-left: none;
    border-top: none;
    border-right: none;
}

.container-fluid.container-2-col {
    display: block;
}

    .container-fluid.container-2-col .col-left {
        margin-right: 30px;
    }

    .container-fluid.container-2-col .konfirmasi-form .col-right {
        position: relative;
        padding-bottom: 100px;
    }

    .container-fluid.container-2-col .konfirmasi-form .content {
        height: 100%;
    }

        .container-fluid.container-2-col .konfirmasi-form .content .submit-wrap {
            position: absolute;
            bottom: 0px;
            box-sizing: border-box;
            width: auto;
            left: 60px;
            right: 60px;
            margin-bottom: 40px;
        }

    .container-fluid.container-2-col .konfirmasi-form.height-auto .content,
    .container-fluid.container-2-col .konfirmasi-form.sticky-col .content {
        height: auto
    }

.btn.btn-inline {
    display: inline-block;
    padding-left: 20px;
    padding-right: 20px;
}

.container-checkbox {
    display: block;
    position: relative;
    cursor: pointer;
    padding-left: 25px;
    margin-bottom: 0;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

    /* Hide the browser's default checkbox */
    .container-checkbox input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0;
    left: 40px;
    height: 18px;
    width: 18px;
    background-color: #fff;
}

/* On mouse-over, add a grey background color */
.container-checkbox:hover input ~ .checkmark {
    background-color: #fff;
}

/* When the checkbox is checked, add a blue background */
.container-checkbox input:checked ~ .checkmark {
    background-color: #3594DF;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container-checkbox input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid #fff;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

footer {
    background-color: #fff;
    padding: 55px 0;
}

    footer .copyright {
        text-align: center;
        font-size: 12px;
    }

.modal-dialog {
    overflow-y: initial !important;
}

    .modal-dialog .modal-lg {
        max-width: 780px;
    }

    .modal-dialog .modal-header {
        
        background-color: #F1F1F1;
        border-bottom: 1px solid #e9ecef;
    }

        .modal-dialog .modal-header .close span {
            width: 24px;
            height: 24px;
            display: block;
            background: url("../Images/icons/close.png") no-repeat;
        }

        .modal-dialog .modal-header .modal-title {
            font-weight: bold;
        }

        .modal-dialog .modal-header h1.modal-title {
            font-family: 'Arial', 'Helvetica', sans-serif;
        }

    .modal-dialog .modal-body {
        max-height: 206px;
        overflow-y: auto;
        padding: 10px 80px 0 80px;
    }

.assent-checkbox .assentcb-input[type=checkbox] {
    display: none;
}

.assent-checkbox .assentcb-label {
    font-weight: 400;
    padding: 0 0 0 35px;
    position: relative;
    cursor: pointer;
}

.assent-checkbox .assentcb-label:before {
    background: url(../Images/icon-checkbox.png) left center no-repeat;
    display: block;
    position: absolute;
    left: 0;
}

.assent-checkbox .assentcb-label:before,
.unsure-checkbox label:before {
    content: "\00a0";
    height: 18px;
    margin-top: -10px;
    top: 50%;
    width: 18px;
}

.assent-checkbox .assentcb-input[type=checkbox]:checked ~ label:before {
    background-position: right center;
}

#colorbox, #cboxOverlay, #cboxWrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow: hidden;
}

#cboxWrapper {
    max-width: none;
}

#cboxOverlay {
    position: fixed;
    width: 100%;
    height: 100%;
}

#cboxMiddleLeft, #cboxBottomLeft {
    clear: left;
}

#cboxContent {
    position: relative;
}

#cboxLoadedContent {
    /*overflow: auto;*/
    -webkit-overflow-scrolling: touch;
}

#cboxTitle {
    margin: 0;
}

#cboxLoadingOverlay, #cboxLoadingGraphic {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow {
    cursor: pointer;
}

.cboxPhoto {
    float: left;
    margin: auto;
    border: 0;
    display: block;
    max-width: none;
    -ms-interpolation-mode: bicubic;
}

.cboxIframe {
    width: 100%;
    height: 100%;
    display: block;
    border: 0;
    padding: 0;
    margin: 0;
}

#colorbox, #cboxContent, #cboxLoadedContent {
    box-sizing: content-box;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
}

#cboxOverlay {
    background: #fff;
    opacity: .9;
    filter: alpha(opacity=90);
}

#colorbox {
    outline: 0;
}

#cboxContent {
    margin-top: 32px;
    overflow: visible;
    background: none;
}

.cboxIframe {
    background: #fff;
}

#cboxError {
    padding: 50px;
    border: 1px solid #ccc;
}

#cboxLoadedContent {
    background: none;
    padding: 0;
}

#cboxLoadingGraphic {
    background: url(../Images/loading.gif) no-repeat center center;
}

#cboxLoadingOverlay {
    background: #000;
}

#cboxTitle {
    position: absolute;
    top: -22px;
    left: 0;
    color: #000;
}

#cboxCurrent {
    position: absolute;
    top: -22px;
    right: 205px;
    text-indent: -9999px;
}

#cboxPrevious, #cboxNext, #cboxSlideshow, #cboxClose {
    border: 0;
    padding: 0;
    margin: 0;
    overflow: visible;
    text-indent: -9999px;
    width: 20px;
    height: 20px;
    position: absolute;
    top: -20px;
    background: url(../Images/controls.png) no-repeat 0 0;
}

    #cboxPrevious:active, #cboxNext:active, #cboxSlideshow:active, #cboxClose:active {
        outline: 0;
    }

#cboxPrevious {
    background-position: 0 0;
    right: 44px;
}

    #cboxPrevious:hover {
        background-position: 0 -25px;
    }

#cboxNext {
    background-position: -25px 0;
    right: 22px;
}

    #cboxNext:hover {
        background-position: -25px -25px;
    }

#cboxClose {
    background-position: -50px 0;
    right: 0;
}

    #cboxClose:hover {
        background-position: -50px -25px;
    }

.cboxSlideshow_on #cboxPrevious, .cboxSlideshow_off #cboxPrevious {
    right: 66px;
}

.cboxSlideshow_on #cboxSlideshow {
    background-position: -75px -25px;
    right: 44px;
}

    .cboxSlideshow_on #cboxSlideshow:hover {
        background-position: -100px -25px;
    }

.cboxSlideshow_off #cboxSlideshow {
    background-position: -100px 0;
    right: 44px;
}

    .cboxSlideshow_off #cboxSlideshow:hover {
        background-position: -75px -25px;
    }

.assentcb-input {
    margin: 10px;
    bottom: 20px;
    position: relative;
}

.modal-footer-panel {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0px 40px;
}

    .modal-footer-panel .btn {
        margin: 0 7px;
    }



#pnlSoalTop {
    display: block
}


#pnlSoalTop {
    position: relative
}

    #pnlSoalTop .textImage.blur {
        filter: blur(8px)
    }

    #pnlSoalTop .pnlSoalShowButton {
        position: absolute;
        width: 100px;
        height: 100px;
        top: 50%;
        left: 50%;
        margin: -50px;
        background: url(../Images/component/jplayer.blue.monday.video.play.png)no-repeat;
        cursor: pointer;
        z-index: 1;
        opacity: .8
    }

        #pnlSoalTop .pnlSoalShowButton:hover {
            opacity: 1
        }

/*! Blue Monday Skin for jPlayer 2.9.2 ~ (c) 2009-2014 Happyworm Ltd ~ MIT License */

.jp-audio-stream:focus,
.jp-audio:focus,
.jp-video:focus {
    outline: 0
}

.jp-audio button::-moz-focus-inner,
.jp-audio-stream button::-moz-focus-inner,
.jp-video button::-moz-focus-inner {
    border: 0
}

.jp-audio,
.jp-audio-stream,
.jp-video {
    font-size: 16px;
    font-family: Verdana, Arial, sans-serif;
    line-height: 1.6;
    color: #666;
    border: 4px solid #c9c9c9;
    background-color: #eee
}

.jp-audio {
    width: 420px
}

.jp-audio-stream {
    width: 182px
}

.jp-video-270p {
    width: 480px
}

.jp-video-360p {
    width: 640px
}

.jp-video-full {
    width: 480px;
    height: 270px;
    position: static !important;
    position: relative
}

    .jp-video-full div div {
        z-index: 1000
    }

    .jp-video-full .jp-jplayer {
        top: 0;
        left: 0;
        position: fixed !important;
        position: relative;
        overflow: hidden
    }

    .jp-video-full .jp-gui {
        position: fixed !important;
        position: static;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1001
    }

    .jp-video-full .jp-interface {
        position: absolute !important;
        position: relative;
        bottom: 0;
        left: 0
    }

.jp-interface {
    position: relative;
    background-color: #eee;
    width: 100%
}

.jp-audio .jp-interface,
.jp-audio-stream .jp-interface {
    height: 80px
}

.jp-video .jp-interface {
    border-top: 1px solid #009be3
}

.jp-controls button,
.jp-volume-controls button {
    text-indent: -9999px;
    border: none;
    cursor: pointer;
    overflow: hidden
}

.jp-controls-holder {
    clear: both;
    width: 440px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    top: -8px
}

.jp-interface .jp-controls {
    margin: 0;
    padding: 0;
    overflow: hidden
}

.jp-audio .jp-controls {
    width: 380px;
    padding: 20px 20px 0
}

.jp-audio-stream .jp-controls {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 142px
}

.jp-video .jp-type-single .jp-controls {
    width: 78px;
    margin-left: 200px
}

.jp-video .jp-type-playlist .jp-controls {
    width: 134px;
    margin-left: 172px
}

.jp-video .jp-controls {
    float: left
}

.jp-controls button {
    display: block;
    float: left
}

.jp-play {
    width: 40px;
    height: 40px;
    background: url(../Images/component/jplayer.blue.monday.jpg) no-repeat
}

    .jp-play:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -41px 0 no-repeat
    }

.jp-state-playing .jp-play {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -42px no-repeat
}

    .jp-state-playing .jp-play:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -41px -42px no-repeat
    }

.jp-next,
.jp-previous,
.jp-stop {
    width: 28px;
    height: 28px;
    margin-top: 6px
}

.jp-stop {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -83px no-repeat;
    margin-left: 10px
}

    .jp-stop:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -29px -83px no-repeat
    }

.jp-previous {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -112px no-repeat
}

    .jp-previous:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -29px -112px no-repeat
    }

.jp-next {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -141px no-repeat
}

    .jp-next:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -29px -141px no-repeat
    }

.jp-progress {
    overflow: hidden;
    background-color: #ddd
}

.jp-audio .jp-progress {
    position: absolute;
    top: 32px;
    height: 15px
}

.jp-audio .jp-type-single .jp-progress {
    left: 110px;
    width: 186px
}

.jp-audio .jp-type-playlist .jp-progress {
    left: 166px;
    width: 130px
}

.jp-video .jp-progress {
    top: 0;
    left: 0;
    width: 100%;
    height: 10px
}

.jp-seek-bar {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -202px repeat-x;
    width: 0;
    height: 100%;
    cursor: pointer
}

.jp-play-bar {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -218px repeat-x;
    width: 0;
    height: 100%
}

.jp-seeking-bg {
    background: url(../Images/component/jplayer.blue.monday.seeking.gif)
}

.jp-state-no-volume .jp-volume-controls {
    display: none
}

.jp-volume-controls {
    position: absolute;
    top: 32px;
    left: 308px;
    width: 200px
}

.jp-audio-stream .jp-volume-controls {
    left: 70px
}

.jp-video .jp-volume-controls {
    top: 12px;
    left: 50px
}

.jp-volume-controls button {
    display: block;
    position: absolute
}

.jp-mute,
.jp-volume-max {
    width: 18px;
    height: 15px
}

.jp-mute {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -170px no-repeat
}

    .jp-mute:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -19px -170px no-repeat
    }

.jp-state-muted .jp-mute {
    background: url(../Images/component/jplayer.blue.monday.jpg) -60px -170px no-repeat
}

    .jp-state-muted .jp-mute:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -79px -170px no-repeat
    }

.jp-volume-max {
    left: 74px;
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -186px no-repeat
}

    .jp-volume-max:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -19px -186px no-repeat
    }

.jp-volume-bar {
    position: absolute;
    overflow: hidden;
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -250px repeat-x;
    top: 5px;
    left: 22px;
    width: 46px;
    height: 5px;
    cursor: pointer
}

.jp-volume-bar-value {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -256px repeat-x;
    width: 0;
    height: 5px
}

.jp-audio .jp-time-holder {
    position: absolute;
    top: 50px
}

.jp-audio .jp-type-single .jp-time-holder {
    left: 110px;
    width: 186px
}

.jp-audio .jp-type-playlist .jp-time-holder {
    left: 166px;
    width: 130px
}

.jp-current-time,
.jp-duration {
    width: 60px;
    font-size: .64em;
    font-style: oblique
}

.jp-current-time {
    float: left;
    display: inline;
    cursor: default
}

.jp-duration {
    float: right;
    display: inline;
    text-align: right;
    cursor: pointer
}

.jp-video .jp-current-time {
    margin-left: 20px
}

.jp-video .jp-duration {
    margin-right: 20px
}

.jp-details {
    font-weight: 700;
    text-align: center;
    cursor: default
}

.jp-details,
.jp-playlist {
    width: 100%;
    background-color: #ccc;
    border-top: 1px solid #009be3
}

.jp-type-playlist .jp-details,
.jp-type-single .jp-details {
    border-top: none
}

.jp-details .jp-title {
    margin: 0;
    padding: 5px 20px;
    font-size: .72em;
    font-weight: 700
}

.jp-playlist ul {
    list-style-type: none;
    margin: 0;
    padding: 0 20px;
    font-size: .72em
}

.jp-playlist li {
    padding: 5px 0 4px 20px;
    border-bottom: 1px solid #eee
}

    .jp-playlist li div {
        display: inline
    }

div.jp-type-playlist div.jp-playlist li:last-child {
    padding: 5px 0 5px 20px;
    border-bottom: none
}

div.jp-type-playlist div.jp-playlist li.jp-playlist-current {
    list-style-type: square;
    list-style-position: inside;
    padding-left: 7px
}

div.jp-type-playlist div.jp-playlist a {
    color: #333;
    text-decoration: none
}

    div.jp-type-playlist div.jp-playlist a.jp-playlist-current,
    div.jp-type-playlist div.jp-playlist a:hover {
        color: #0d88c1
    }

    div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove {
        float: right;
        display: inline;
        text-align: right;
        margin-right: 10px;
        font-weight: 700;
        color: #666
    }

        div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove:hover {
            color: #0d88c1
        }

div.jp-type-playlist div.jp-playlist span.jp-free-media {
    float: right;
    display: inline;
    text-align: right;
    margin-right: 10px
}

.jp-toggles button,
.jp-video-play-icon {
    display: block;
    text-indent: -9999px;
    border: none;
    cursor: pointer
}

div.jp-type-playlist div.jp-playlist span.jp-free-media a {
    color: #666
}

    div.jp-type-playlist div.jp-playlist span.jp-free-media a:hover {
        color: #0d88c1
    }

span.jp-artist {
    font-size: .8em;
    color: #666
}

.jp-video-play {
    width: 100%;
    overflow: hidden;
    cursor: pointer;
    background-color: transparent
}

.jp-video-270p .jp-video-play {
    margin-top: -270px;
    height: 270px
}

.jp-video-360p .jp-video-play {
    margin-top: -360px;
    height: 360px
}

.jp-video-full .jp-video-play {
    height: 100%
}

.jp-video-play-icon {
    position: relative;
    width: 112px;
    height: 100px;
    margin-left: -56px;
    margin-top: -50px;
    left: 50%;
    top: 50%;
    background: url(../Images/component/jplayer.blue.monday.video.play.png) no-repeat
}

    .jp-video-play-icon:focus {
        background: url(../Images/component/jplayer.blue.monday.video.play.png) 0 -100px no-repeat
    }

.jp-jplayer,
.jp-jplayer audio {
    width: 0;
    height: 0
}

.jp-jplayer {
    background-color: #000
}

.jp-toggles {
    padding: 0;
    margin: 0 auto;
    overflow: hidden
}

.jp-audio .jp-type-single .jp-toggles {
    width: 25px
}

.jp-audio .jp-type-playlist .jp-toggles {
    width: 55px;
    margin: 0;
    position: absolute;
    left: 325px;
    top: 50px
}

.jp-video .jp-toggles {
    position: absolute;
    right: 16px;
    margin: 10px 0 0;
    width: 100px
}

.jp-toggles button {
    float: left;
    width: 25px;
    height: 18px;
    line-height: 100%
}

.jp-full-screen {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -310px no-repeat;
    margin-left: 20px
}

    .jp-full-screen:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -30px -310px no-repeat
    }

.jp-state-full-screen .jp-full-screen {
    background: url(../Images/component/jplayer.blue.monday.jpg) -60px -310px no-repeat
}

    .jp-state-full-screen .jp-full-screen:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -90px -310px no-repeat
    }

.jp-repeat {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -290px no-repeat
}

    .jp-repeat:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -30px -290px no-repeat
    }

.jp-state-looped .jp-repeat {
    background: url(../Images/component/jplayer.blue.monday.jpg) -60px -290px no-repeat
}

    .jp-state-looped .jp-repeat:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -90px -290px no-repeat
    }

.jp-shuffle {
    background: url(../Images/component/jplayer.blue.monday.jpg) 0 -270px no-repeat;
    margin-left: 5px
}

    .jp-shuffle:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -30px -270px no-repeat
    }

.jp-state-shuffled .jp-shuffle {
    background: url(../Images/component/jplayer.blue.monday.jpg) -60px -270px no-repeat
}

    .jp-state-shuffled .jp-shuffle:focus {
        background: url(../Images/component/jplayer.blue.monday.jpg) -90px -270px no-repeat
    }

#awal{
    color:#FFF;
    font-family:Arial, Helvetica, sans-serif;
    line-height: 90%;
    margin:0px auto;
    margin-top:20px;
}
#ahir{
    color:#FFF;
    font-family:Arial, Helvetica, sans-serif;
    line-height: 120%;
    margin:0px auto;
    margin-top:10px;
}


#kaki{
    margin-top:-8px;
    margin-left:15px;
    margin-bottom:10px;
    margin-right:15px;
    background-color:#000;
    color:#fff;
    height:400px;   
    }           

#koplembarsoal{
    margin-top:15px;
    margin-left:15px;
    margin-bottom:15px;
    margin-right:15px;
    background-color:#fff;
    height:90px;
    font-size:24px;
    font-weight:bold;
}   
.title {
    font-size: 13pt;
    font-weight: bold;
    margin-left:20px;
    margin-top:-33px;
    top:-33px;  
}
.header {
    background-color: #fff;
    padding-top: 7px;
    padding-bottom:11px;
    margin-left:15px;
    margin-right:15px;
    margin-top:10px;
    margin-bottom:2px;
}
.header.scroll-to-fixed-fixed {
    color: red;
    margin-top:0px;
    border-bottom-style:solid;
    border-color:#ccc;
-webkit-box-shadow: 0 8px 6px -6px #ccc;
       -moz-box-shadow: 0 8px 6px -6px #ccc;
            box-shadow: 0 8px 6px -6px #ccc;

    margin-left:0px;
}
.lanjut {
    background-color: #fff;
    width:100%;
}

#primary {
    float: left;
    width: 480px;
    
}

#content {
    float: left;
    width: 480px;
}

#secondary {
    float: left;
    width: 480px;
}

.kotaksoal{
    width:100%;
    padding:20px;
    border:solid;
    top:30px;
    border-color:#CCC;
    height:100%;
}
.flex-next {
    background-color: #336898;
    width: 20px;
    height: 20px;
    margin: 10px;
    line-height: 20px;
    color: white;
    font-size: 18px;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:10px;
    padding-bottom:10px;
  border-radius: 30px;

}
.flex-ragu {
    background-color:#FC0;
    width: 20px;
    height: 20px;
    margin: 10px;
    line-height: 20px;
    color: white;
    font-size: 18px;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:10px;
    padding-bottom:10px;
    text-decoration:none;
   border-radius: 30px;
}
.flex-prev {
    background-color: #999;
    width: 25px;
    height: 25px;
    margin: 10px;
    line-height: 20px;
    color: white;
    font-size: 18px;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:10px;
    padding-bottom:10px;
   border-radius: 30px;
}
.flex-container {
    height: 100%;
    padding: 0;
    margin: 0;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}
.row {
    width: auto;
    /*border: 1px solid blue;*/
     
}
.flex-item {
    background-color: #336898;
     width: 120px;
    height: 40px;
    margin-right: 0px;
    margin-top:-10px;
    line-height: 20px;
    color: white;
    font-size: 15px;
    font-weight:bold;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:7px;
    padding-bottom:6px;
}   
.flex-abu {
    background-color: #fff;
    width: 200px;
    height: 40px;
    margin-right: 0px;
    margin-top:-10px;
    line-height: 20px;
    color: black;
    font-size: 15px;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:10px;
    padding-bottom:10px;
    float:right;
  border:solid 2px red;
  border-radius: 30px;
}   
.flex-soal {
    background-color: #fff;
    width: 200px;
    height: 40px;
    margin-right: 0px;
  margin-top:-10px;
    line-height: 20px;
    color: black;
    font-size: 15px;
    text-align: center;
  padding-left:12px;
  padding-right:12px; 
  padding-top:10px;
  padding-bottom:10px;
  float:right;
  border:solid 2px red;
  border-radius: 30px;
} 
.flex-biru {
    background-color: #336898;
    width: 120px;
    height: 40px;
    margin-right: 0px;
    margin-top:-10px;
    line-height: 20px;
    color: white;
    font-size: 15px;
    text-align: center;
    padding-left:5px;
    padding-right:5px;  
    padding-top:10px;
    padding-bottom:10px;
    float:right;
  border-radius: 30px;
 
}   
.flex-putih {
    background-color: #fff;
    width: 120px;
    height: 40px;
    margin-right: 0px;
    margin-top:-10px;
    line-height: 20px;
    color: black;
    font-size: 15px;
    font-weight:bold;
    text-align: center;
    padding-left:12px;
    padding-right:12px; 
    padding-top:10px;
    padding-bottom:10px;
    float:left;
}   
.flex-putih1 {
    background-color: #fff;
    width: 80px;
    height: 30px;
    margin-right: 0px;
  margin-top:10px;
    line-height: 20px;
    color: black;
    font-size: 10px;
  font-weight:bold;
    text-align: center;
  padding-left:12px;
  padding-right:12px; 
  padding-top:5px;
  padding-bottom:10px;
  float:left;
  border-radius: 30px;
  
} 


/*! END Blue Monday Skin for jPlayer 2.9.2 ~ (c) 2009-2014 Happyworm Ltd ~ MIT License */