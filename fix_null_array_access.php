<?php
// <PERSON><PERSON><PERSON> to fix null array access issues in PHP 8
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Fixing Null Array Access Issues</h2>";

// Common patterns that need fixing
$patterns_to_fix = [
    // Pattern: $var = mysql_fetch_array($query); $data = $var['field'];
    [
        'description' => 'Direct array access after mysql_fetch_array',
        'pattern' => '/(\$\w+)\s*=\s*mysql_fetch_array\([^)]+\);\s*(\$\w+)\s*=\s*\1\[\'([^\']+)\'\];/',
        'replacement' => '$1 = mysql_fetch_array($2);
if ($1 && is_array($1)) {
    $2 = isset($1[\'$3\']) ? $1[\'$3\'] : \'\';
} else {
    $2 = \'\';
}'
    ]
];

// Files that commonly have this issue
$files_to_check = [
    'panel/pages/daftar_tesbaru.php',
    'panel/pages/daftar_waktu.php', 
    'panel/pages/daftar_waktu_db.php',
    'panel/pages/berita_acara.php',
    'panel/pages/index.php',
    'panel/pages/login.php'
];

$fixes_applied = 0;

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<h3>Checking: $file</h3>";
        $content = file_get_contents($file);
        $original_content = $content;
        
        // Look for common problematic patterns
        $lines = explode("\n", $content);
        $modified = false;
        
        for ($i = 0; $i < count($lines); $i++) {
            $line = $lines[$i];
            
            // Pattern 1: $var = mysql_fetch_array($query); followed by $data = $var['field'];
            if (preg_match('/(\$\w+)\s*=\s*mysql_fetch_array\([^)]+\);/', $line, $matches)) {
                $var_name = $matches[1];
                
                // Check next few lines for direct array access
                for ($j = $i + 1; $j < min($i + 5, count($lines)); $j++) {
                    if (preg_match('/(\$\w+)\s*=\s*' . preg_quote($var_name, '/') . '\[\'([^\']+)\'\];/', $lines[$j], $field_matches)) {
                        $field_var = $field_matches[1];
                        $field_name = $field_matches[2];
                        
                        // Replace the problematic line
                        $lines[$j] = "if ($var_name && is_array($var_name)) {
    $field_var = isset({$var_name}['$field_name']) ? {$var_name}['$field_name'] : '';
} else {
    $field_var = '';
}";
                        $modified = true;
                        echo "<p>✅ Fixed array access for field '$field_name' on line " . ($j + 1) . "</p>";
                        $fixes_applied++;
                    }
                }
            }
        }
        
        if ($modified) {
            $new_content = implode("\n", $lines);
            file_put_contents($file, $new_content);
            echo "<p><strong>✅ File updated: $file</strong></p>";
        } else {
            echo "<p>No issues found in $file</p>";
        }
        
    } else {
        echo "<p>⚠️ File not found: $file</p>";
    }
}

echo "<h3>Summary</h3>";
if ($fixes_applied > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Fixes Applied: $fixes_applied</h4>";
    echo "<p>Null array access issues have been fixed in the checked files.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px;'>";
    echo "<h4>ℹ️ No automatic fixes needed</h4>";
    echo "<p>The checked files appear to be clean or have already been fixed.</p>";
    echo "</div>";
}

echo "<h3>Manual Check Recommendations</h3>";
echo "<p>For complete PHP 8 compatibility, manually review these patterns:</p>";
echo "<ul>";
echo "<li><code>\$var = mysql_fetch_array(\$query); \$data = \$var['field'];</code></li>";
echo "<li><code>\$result['field']</code> without checking if \$result is not null</li>";
echo "<li>Direct array access in loops without null checks</li>";
echo "</ul>";

echo "<h3>Next Steps</h3>";
echo "<p><a href='php8_compatibility_check.php'>Run Full Compatibility Check</a></p>";
echo "<p><a href='panel/'>Test Panel Access</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
